import { ArrayReduceQuestion } from './question';

export class QuestionImpl implements ArrayReduceQuestion {
  myReduce<T>(
    array: T[], 
    callback: (accumulator: T, currentValue: T, currentIndex: number, array: T[]) => T
  ): T;
  myReduce<T, U>(
    array: T[], 
    callback: (accumulator: U, currentValue: T, currentIndex: number, array: T[]) => U, 
    initialValue: U
  ): U;
  myReduce<T, U>(
    array: T[], 
    callback: (accumulator: T | U, currentValue: T, currentIndex: number, array: T[]) => T | U, 
    initialValue?: U
  ): T | U {
    // TODO: Implement your solution here
    throw new Error('Not implemented');
  }
}
