/**
 * Array.prototype.filter Implementation
 * 
 * Implement your own version of Array.prototype.filter that creates a new array
 * with all elements that pass a test implemented by the provided callback function.
 * 
 * Requirements:
 * - Should not modify the original array
 * - Should return a new array containing only elements that pass the test
 * - Should call the callback function for each element with (element, index, array) parameters
 * - Should handle empty arrays correctly
 * - Should preserve the context (thisArg) if provided
 * - The callback should return a truthy value to include the element
 * 
 * Function signature:
 * myFilter<T>(array: T[], callback: (value: T, index: number, array: T[]) => boolean, thisArg?: any): T[]
 */

export interface ArrayFilterQuestion {
  /**
   * Custom implementation of Array.prototype.filter
   * @param array - The array to filter
   * @param callback - Function that tests each element (should return truthy to keep element)
   * @param thisArg - Optional context for the callback function
   * @returns New array with elements that pass the test
   */
  myFilter<T>(
    array: T[], 
    callback: (value: T, index: number, array: T[]) => boolean, 
    thisArg?: any
  ): T[];
}

// Example usage:
// const numbers = [1, 2, 3, 4, 5, 6];
// const evens = myFilter(numbers, (x) => x % 2 === 0);
// console.log(evens); // [2, 4, 6]
// console.log(numbers); // [1, 2, 3, 4, 5, 6] (unchanged)
