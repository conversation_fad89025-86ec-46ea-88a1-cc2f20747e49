import { describe, it, expect } from 'vitest';
import { QuestionImpl } from './question-impl';

describe('Array.prototype.filter Implementation', () => {
  const impl = new QuestionImpl();

  it('should filter elements based on callback condition', () => {
    const numbers = [1, 2, 3, 4, 5, 6];
    const evens = impl.myFilter(numbers, (x) => x % 2 === 0);
    
    expect(evens).toEqual([2, 4, 6]);
    expect(numbers).toEqual([1, 2, 3, 4, 5, 6]); // Original array unchanged
  });

  it('should work with different data types', () => {
    const words = ['apple', 'banana', 'cherry', 'date'];
    const longWords = impl.myFilter(words, (word) => word.length > 5);
    
    expect(longWords).toEqual(['banana', 'cherry']);
  });

  it('should pass correct parameters to callback', () => {
    const array = ['a', 'b', 'c'];
    const results: Array<{value: string, index: number, array: string[]}> = [];
    
    impl.myFilter(array, (value, index, arr) => {
      results.push({ value, index, array: arr });
      return index % 2 === 0; // Keep elements at even indices
    });
    
    expect(results).toEqual([
      { value: 'a', index: 0, array: ['a', 'b', 'c'] },
      { value: 'b', index: 1, array: ['a', 'b', 'c'] },
      { value: 'c', index: 2, array: ['a', 'b', 'c'] }
    ]);
  });

  it('should handle empty arrays', () => {
    const empty: number[] = [];
    const result = impl.myFilter(empty, (x) => x > 0);
    
    expect(result).toEqual([]);
  });

  it('should handle thisArg context', () => {
    const threshold = { min: 3 };
    const numbers = [1, 2, 3, 4, 5];
    
    const result = impl.myFilter(numbers, function(this: typeof threshold, x) {
      return x >= this.min;
    }, threshold);
    
    expect(result).toEqual([3, 4, 5]);
  });

  it('should return a new array instance', () => {
    const original = [1, 2, 3];
    const result = impl.myFilter(original, () => true);
    
    expect(result).not.toBe(original);
    expect(result).toEqual(original);
  });

  it('should handle no matches', () => {
    const numbers = [1, 3, 5, 7];
    const evens = impl.myFilter(numbers, (x) => x % 2 === 0);
    
    expect(evens).toEqual([]);
  });

  it('should handle all matches', () => {
    const numbers = [2, 4, 6, 8];
    const evens = impl.myFilter(numbers, (x) => x % 2 === 0);
    
    expect(evens).toEqual([2, 4, 6, 8]);
  });

  it('should handle complex filtering conditions', () => {
    const people = [
      { name: 'Alice', age: 25, active: true },
      { name: 'Bob', age: 30, active: false },
      { name: 'Charlie', age: 35, active: true },
      { name: 'Diana', age: 20, active: false }
    ];
    
    const activeAdults = impl.myFilter(people, (person) => 
      person.active && person.age >= 25
    );
    
    expect(activeAdults).toEqual([
      { name: 'Alice', age: 25, active: true },
      { name: 'Charlie', age: 35, active: true }
    ]);
  });

  it('should handle truthy/falsy values correctly', () => {
    const mixed = [0, 1, '', 'hello', null, 'world', undefined, false, true];
    const truthy = impl.myFilter(mixed, (x) => !!x);
    
    expect(truthy).toEqual([1, 'hello', 'world', true]);
  });
});
