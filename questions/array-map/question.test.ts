import { describe, it, expect } from 'vitest';
import { QuestionImpl } from './question-impl';

describe('Array.prototype.map Implementation', () => {
  const impl = new QuestionImpl();

  it('should transform elements using the callback function', () => {
    const numbers = [1, 2, 3, 4, 5];
    const doubled = impl.myMap(numbers, (x) => x * 2);
    
    expect(doubled).toEqual([2, 4, 6, 8, 10]);
    expect(numbers).toEqual([1, 2, 3, 4, 5]); // Original array unchanged
  });

  it('should work with different data types', () => {
    const strings = ['hello', 'world'];
    const lengths = impl.myMap(strings, (str) => str.length);
    
    expect(lengths).toEqual([5, 5]);
  });

  it('should pass correct parameters to callback', () => {
    const array = ['a', 'b', 'c'];
    const results: Array<{value: string, index: number, array: string[]}> = [];
    
    impl.myMap(array, (value, index, arr) => {
      results.push({ value, index, array: arr });
      return value.toUpperCase();
    });
    
    expect(results).toEqual([
      { value: 'a', index: 0, array: ['a', 'b', 'c'] },
      { value: 'b', index: 1, array: ['a', 'b', 'c'] },
      { value: 'c', index: 2, array: ['a', 'b', 'c'] }
    ]);
  });

  it('should handle empty arrays', () => {
    const empty: number[] = [];
    const result = impl.myMap(empty, (x) => x * 2);
    
    expect(result).toEqual([]);
  });

  it('should handle thisArg context', () => {
    const multiplier = { factor: 3 };
    const numbers = [1, 2, 3];
    
    const result = impl.myMap(numbers, function(this: typeof multiplier, x) {
      return x * this.factor;
    }, multiplier);
    
    expect(result).toEqual([3, 6, 9]);
  });

  it('should return a new array instance', () => {
    const original = [1, 2, 3];
    const result = impl.myMap(original, (x) => x);
    
    expect(result).not.toBe(original);
    expect(result).toEqual(original);
  });

  it('should handle complex transformations', () => {
    const objects = [
      { name: 'Alice', age: 25 },
      { name: 'Bob', age: 30 },
      { name: 'Charlie', age: 35 }
    ];
    
    const names = impl.myMap(objects, (person) => person.name);
    expect(names).toEqual(['Alice', 'Bob', 'Charlie']);
    
    const descriptions = impl.myMap(objects, (person, index) => 
      `${index + 1}. ${person.name} is ${person.age} years old`
    );
    expect(descriptions).toEqual([
      '1. Alice is 25 years old',
      '2. Bob is 30 years old',
      '3. Charlie is 35 years old'
    ]);
  });
});
