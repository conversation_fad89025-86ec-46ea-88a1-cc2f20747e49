/**
 * Array.prototype.map Implementation
 * 
 * Implement your own version of Array.prototype.map that transforms each element
 * of an array using a callback function and returns a new array with the transformed elements.
 * 
 * Requirements:
 * - Should not modify the original array
 * - Should return a new array with the same length as the original
 * - Should call the callback function for each element with (element, index, array) parameters
 * - Should handle empty arrays correctly
 * - Should preserve the context (thisArg) if provided
 * 
 * Function signature:
 * myMap<T, U>(array: T[], callback: (value: T, index: number, array: T[]) => U, thisArg?: any): U[]
 */

export interface ArrayMapQuestion {
  /**
   * Custom implementation of Array.prototype.map
   * @param array - The array to transform
   * @param callback - Function that transforms each element
   * @param thisArg - Optional context for the callback function
   * @returns New array with transformed elements
   */
  myMap<T, U>(
    array: T[], 
    callback: (value: T, index: number, array: T[]) => U, 
    thisArg?: any
  ): U[];
}

// Example usage:
// const numbers = [1, 2, 3, 4, 5];
// const doubled = myMap(numbers, (x) => x * 2);
// console.log(doubled); // [2, 4, 6, 8, 10]
// console.log(numbers); // [1, 2, 3, 4, 5] (unchanged)
