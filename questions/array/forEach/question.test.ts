import { describe, it, expect, vi } from 'vitest';
import { myForEach } from './question';

describe('Array.prototype.forEach Implementation', () => {
  

  it('should execute callback for each element', () => {
    const numbers = [1, 2, 3, 4, 5];
    const results: number[] = [];
    
    const result = myForEach(numbers, (num) => {
      results.push(num * 2);
    });
    
    expect(results).toEqual([2, 4, 6, 8, 10]);
    expect(result).toBeUndefined();
    expect(numbers).toEqual([1, 2, 3, 4, 5]); // Original array unchanged
  });

  it('should pass correct parameters to callback', () => {
    const array = ['a', 'b', 'c'];
    const calls: Array<{value: string, index: number, array: string[]}> = [];
    
    myForEach(array, (value, index, arr) => {
      calls.push({ value, index, array: arr });
    });
    
    expect(calls).toEqual([
      { value: 'a', index: 0, array: ['a', 'b', 'c'] },
      { value: 'b', index: 1, array: ['a', 'b', 'c'] },
      { value: 'c', index: 2, array: ['a', 'b', 'c'] }
    ]);
  });

  it('should handle empty arrays', () => {
    const empty: number[] = [];
    const mockCallback = vi.fn();
    
    const result = myForEach(empty, mockCallback);
    
    expect(mockCallback).not.toHaveBeenCalled();
    expect(result).toBeUndefined();
  });

  it('should handle thisArg context', () => {
    const multiplier = { factor: 3 };
    const numbers = [1, 2, 3];
    const results: number[] = [];
    
    myForEach(numbers, function(this: typeof multiplier, num) {
      results.push(num * this.factor);
    }, multiplier);
    
    expect(results).toEqual([3, 6, 9]);
  });

  it('should not return anything', () => {
    const numbers = [1, 2, 3];
    const result = myForEach(numbers, () => {});
    
    expect(result).toBeUndefined();
  });

  it('should allow callback to modify external variables', () => {
    const numbers = [1, 2, 3, 4, 5];
    let sum = 0;
    let count = 0;
    
    myForEach(numbers, (num, index) => {
      sum += num;
      count = index + 1;
    });
    
    expect(sum).toBe(15);
    expect(count).toBe(5);
  });

  it('should work with different data types', () => {
    const mixed = [1, 'hello', true, { name: 'test' }, [1, 2, 3]];
    const types: string[] = [];
    
    myForEach(mixed, (item) => {
      types.push(typeof item);
    });
    
    expect(types).toEqual(['number', 'string', 'boolean', 'object', 'object']);
  });

  it('should handle objects in array', () => {
    const people = [
      { name: 'Alice', age: 25 },
      { name: 'Bob', age: 30 },
      { name: 'Charlie', age: 35 }
    ];
    const names: string[] = [];
    const ages: number[] = [];
    
    myForEach(people, (person, index) => {
      names.push(`${index + 1}. ${person.name}`);
      ages.push(person.age);
    });
    
    expect(names).toEqual(['1. Alice', '2. Bob', '3. Charlie']);
    expect(ages).toEqual([25, 30, 35]);
  });

  it('should call callback exact number of times', () => {
    const numbers = [10, 20, 30, 40];
    const mockCallback = vi.fn();
    
    myForEach(numbers, mockCallback);
    
    expect(mockCallback).toHaveBeenCalledTimes(4);
    expect(mockCallback).toHaveBeenNthCalledWith(1, 10, 0, numbers);
    expect(mockCallback).toHaveBeenNthCalledWith(2, 20, 1, numbers);
    expect(mockCallback).toHaveBeenNthCalledWith(3, 30, 2, numbers);
    expect(mockCallback).toHaveBeenNthCalledWith(4, 40, 3, numbers);
  });

  it('should handle single element array', () => {
    const single = [42];
    const results: number[] = [];
    
    myForEach(single, (num, index) => {
      results.push(num + index);
    });
    
    expect(results).toEqual([42]);
  });

  it('should work with nested operations', () => {
    const matrix = [[1, 2], [3, 4], [5, 6]];
    const flattened: number[] = [];
    
    myForEach(matrix, (row) => {
      myForEach(row, (num) => {
        flattened.push(num);
      });
    });
    
    expect(flattened).toEqual([1, 2, 3, 4, 5, 6]);
  });

  it('should handle callback that throws error', () => {
    const numbers = [1, 2, 3];
    
    expect(() => {
      myForEach(numbers, (num) => {
        if (num === 2) {
          throw new Error('Test error');
        }
      });
    }).toThrow('Test error');
  });
});
