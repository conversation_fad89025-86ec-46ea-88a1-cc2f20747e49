/**
 * Array.prototype.forEach Implementation
 * 
 * Implement your own version of Array.prototype.forEach that executes a provided
 * function once for each array element.
 * 
 * Requirements:
 * - Should not modify the original array (unless the callback does)
 * - Should not return anything (undefined)
 * - Should call the callback function for each element with (element, index, array) parameters
 * - Should handle empty arrays correctly (do nothing)
 * - Should preserve the context (thisArg) if provided
 * - Should not call callback for missing elements in sparse arrays
 * 
 * Function signature:
 * myForEach<T>(array: T[], callback: (value: T, index: number, array: T[]) => void, thisArg?: any): void
 */

/**
 * Custom implementation of Array.prototype.forEach
 * @param array - The array to iterate over
 * @param callback - Function to execute for each element
 * @param thisArg - Optional context for the callback function
 * @returns undefined
 */
export function myForEach<T>(
  array: T[],
  callback: (value: T, index: number, array: T[]) => void,
  thisArg?: any
): void {
  // TODO: Implement your solution here
  throw new Error('Not implemented');
}

// Example usage:
// const numbers = [1, 2, 3, 4, 5];
// myForEach(numbers, (num, index) => {
//   console.log(`Index ${index}: ${num}`);
// });
// Output:
// Index 0: 1
// Index 1: 2
// Index 2: 3
// Index 3: 4
// Index 4: 5
