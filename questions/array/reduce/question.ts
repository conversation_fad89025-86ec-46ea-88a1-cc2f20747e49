/**
 * Array.prototype.reduce Implementation
 * 
 * Implement your own version of Array.prototype.reduce that executes a reducer function
 * on each element of the array, resulting in a single output value.
 * 
 * Requirements:
 * - Should not modify the original array
 * - Should call the reducer function for each element with (accumulator, currentValue, index, array) parameters
 * - Should handle both cases: with and without an initial value
 * - Should throw TypeError if array is empty and no initial value is provided
 * - Should handle empty arrays correctly when initial value is provided
 * - Should start from index 0 if initial value is provided, index 1 if not
 * 
 * Function signatures:
 * myReduce<T>(array: T[], callback: (acc: T, curr: T, index: number, array: T[]) => T): T
 * myReduce<T, U>(array: T[], callback: (acc: U, curr: T, index: number, array: T[]) => U, initialValue: U): U
 */

/**
 * Custom implementation of Array.prototype.reduce without initial value
 * @param array - The array to reduce
 * @param callback - Function that reduces each element
 * @returns The final accumulated value
 */
export function myReduce<T>(
  array: T[],
  callback: (accumulator: T, currentValue: T, currentIndex: number, array: T[]) => T
): T;

/**
 * Custom implementation of Array.prototype.reduce with initial value
 * @param array - The array to reduce
 * @param callback - Function that reduces each element
 * @param initialValue - Initial value for the accumulator
 * @returns The final accumulated value
 */
export function myReduce<T, U>(
  array: T[],
  callback: (accumulator: U, currentValue: T, currentIndex: number, array: T[]) => U,
  initialValue: U
): U;

export function myReduce<T, U>(
  array: T[],
  callback: (accumulator: T | U, currentValue: T, currentIndex: number, array: T[]) => T | U,
  initialValue?: U
): T | U {
  // TODO: Implement your solution here
  throw new Error('Not implemented');
}

// Example usage:
// const numbers = [1, 2, 3, 4, 5];
// const sum = myReduce(numbers, (acc, curr) => acc + curr); // 15
// const product = myReduce(numbers, (acc, curr) => acc * curr, 1); // 120
// const max = myReduce(numbers, (acc, curr) => Math.max(acc, curr)); // 5
