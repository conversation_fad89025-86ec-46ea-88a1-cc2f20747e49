import { describe, it, expect } from 'vitest';
import { myReduce } from './question';

describe('Array.prototype.reduce Implementation', () => {
  

  describe('without initial value', () => {
    it('should sum numbers', () => {
      const numbers = [1, 2, 3, 4, 5];
      const sum = myReduce(numbers, (acc, curr) => acc + curr);
      
      expect(sum).toBe(15);
      expect(numbers).toEqual([1, 2, 3, 4, 5]); // Original array unchanged
    });

    it('should find maximum value', () => {
      const numbers = [3, 1, 4, 1, 5, 9, 2, 6];
      const max = myReduce(numbers, (acc, curr) => Math.max(acc, curr));
      
      expect(max).toBe(9);
    });

    it('should concatenate strings', () => {
      const words = ['Hello', ' ', 'World', '!'];
      const sentence = myReduce(words, (acc, curr) => acc + curr);
      
      expect(sentence).toBe('Hello World!');
    });

    it('should throw error for empty array', () => {
      const empty: number[] = [];
      
      expect(() => {
        myReduce(empty, (acc, curr) => acc + curr);
      }).toThrow();
    });

    it('should return single element for single-element array', () => {
      const single = [42];
      const result = myReduce(single, (acc, curr) => acc + curr);
      
      expect(result).toBe(42);
    });
  });

  describe('with initial value', () => {
    it('should sum numbers with initial value', () => {
      const numbers = [1, 2, 3, 4, 5];
      const sum = myReduce(numbers, (acc, curr) => acc + curr, 0);
      
      expect(sum).toBe(15);
    });

    it('should multiply numbers with initial value', () => {
      const numbers = [2, 3, 4];
      const product = myReduce(numbers, (acc, curr) => acc * curr, 1);
      
      expect(product).toBe(24);
    });

    it('should handle empty array with initial value', () => {
      const empty: number[] = [];
      const result = myReduce(empty, (acc, curr) => acc + curr, 10);
      
      expect(result).toBe(10);
    });

    it('should build object from array', () => {
      const items = ['apple', 'banana', 'cherry'];
      const indexed = myReduce(items, (acc, curr, index) => {
        acc[index] = curr;
        return acc;
      }, {} as Record<number, string>);
      
      expect(indexed).toEqual({
        0: 'apple',
        1: 'banana',
        2: 'cherry'
      });
    });

    it('should count occurrences', () => {
      const letters = ['a', 'b', 'a', 'c', 'b', 'a'];
      const counts = myReduce(letters, (acc, curr) => {
        acc[curr] = (acc[curr] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      expect(counts).toEqual({
        a: 3,
        b: 2,
        c: 1
      });
    });

    it('should flatten array', () => {
      const nested = [[1, 2], [3, 4], [5, 6]];
      const flattened = myReduce(nested, (acc, curr) => acc.concat(curr), [] as number[]);
      
      expect(flattened).toEqual([1, 2, 3, 4, 5, 6]);
    });
  });

  describe('callback parameters', () => {
    it('should pass correct parameters to callback', () => {
      const array = [10, 20, 30];
      const calls: Array<{acc: number, curr: number, index: number, array: number[]}> = [];
      
      myReduce(array, (acc, curr, index, arr) => {
        calls.push({ acc, curr, index, array: arr });
        return acc + curr;
      });
      
      expect(calls).toEqual([
        { acc: 10, curr: 20, index: 1, array: [10, 20, 30] },
        { acc: 30, curr: 30, index: 2, array: [10, 20, 30] }
      ]);
    });

    it('should pass correct parameters with initial value', () => {
      const array = [10, 20, 30];
      const calls: Array<{acc: number, curr: number, index: number, array: number[]}> = [];
      
      myReduce(array, (acc, curr, index, arr) => {
        calls.push({ acc, curr, index, array: arr });
        return acc + curr;
      }, 0);
      
      expect(calls).toEqual([
        { acc: 0, curr: 10, index: 0, array: [10, 20, 30] },
        { acc: 10, curr: 20, index: 1, array: [10, 20, 30] },
        { acc: 30, curr: 30, index: 2, array: [10, 20, 30] }
      ]);
    });
  });

  describe('complex transformations', () => {
    it('should group objects by property', () => {
      const people = [
        { name: 'Alice', department: 'Engineering' },
        { name: 'Bob', department: 'Marketing' },
        { name: 'Charlie', department: 'Engineering' },
        { name: 'Diana', department: 'Marketing' }
      ];
      
      const grouped = myReduce(people, (acc, person) => {
        const dept = person.department;
        if (!acc[dept]) {
          acc[dept] = [];
        }
        acc[dept].push(person.name);
        return acc;
      }, {} as Record<string, string[]>);
      
      expect(grouped).toEqual({
        Engineering: ['Alice', 'Charlie'],
        Marketing: ['Bob', 'Diana']
      });
    });

    it('should calculate running totals', () => {
      const numbers = [1, 2, 3, 4, 5];
      const runningTotals = myReduce(numbers, (acc, curr) => {
        const newTotal = (acc.length > 0 ? acc[acc.length - 1] : 0) + curr;
        acc.push(newTotal);
        return acc;
      }, [] as number[]);
      
      expect(runningTotals).toEqual([1, 3, 6, 10, 15]);
    });
  });
});
