/**
 * Custom setTimeout and clearTimeout Implementation
 * 
 * Build your own setTimeout and clearTimeout functions that work similarly to the native browser/Node.js versions.
 * 
 * Requirements:
 * 1. mySetTimeout should execute a callback after a specified delay
 * 2. mySetTimeout should return a timer ID that can be used to cancel the timer
 * 3. myClearTimeout should cancel a timer using its ID
 * 4. Should handle multiple timers running concurrently
 * 5. Should handle edge cases like negative delays, clearing non-existent timers
 * 6. Should support passing arguments to the callback function
 * 7. Timer IDs should be unique and reusable after clearing
 * 
 * Implementation Notes:
 * - You cannot use the native setTimeout/clearTimeout functions
 * - You can use Date.now() or performance.now() for timing
 * - Consider using setInterval with a small interval to check for expired timers
 * - Handle memory cleanup properly to avoid leaks
 */

export type TimerId = number;
export type TimerCallback = (...args: any[]) => void;

/**
 * Custom implementation of setTimeout
 * @param callback - Function to execute after the delay
 * @param delay - Delay in milliseconds (minimum 0)
 * @param args - Arguments to pass to the callback
 * @returns Timer ID that can be used with clearTimeout
 */
export function mySetTimeout(
  callback: TimerCallback,
  delay: number = 0,
  ...args: any[]
): TimerId {
  // TODO: Implement your solution here
  throw new Error('Not implemented');
}

/**
 * Custom implementation of clearTimeout
 * @param timerId - Timer ID returned by setTimeout
 */
export function myClearTimeout(timerId: TimerId): void {
  // TODO: Implement your solution here
  throw new Error('Not implemented');
}

// Example usage:
// const timerId = mySetTimeout(() => console.log('Hello!'), 1000);
// myClearTimeout(timerId); // Cancels the timer
// 
// const timerId2 = mySetTimeout((msg, num) => console.log(msg, num), 500, 'Count:', 42);
// // Will log "Count: 42" after 500ms
