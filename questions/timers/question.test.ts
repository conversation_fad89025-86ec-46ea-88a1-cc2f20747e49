import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mySetTimeout, myClearTimeout } from './question';

describe('Custom setTimeout/clearTimeout Implementation', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should execute callback after specified delay', async () => {
    const callback = vi.fn();
    
    mySetTimeout(callback, 1000);
    
    expect(callback).not.toHaveBeenCalled();
    
    vi.advanceTimersByTime(999);
    expect(callback).not.toHaveBeenCalled();
    
    vi.advanceTimersByTime(1);
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should return a unique timer ID', () => {
    const callback = vi.fn();
    
    const id1 = mySetTimeout(callback, 100);
    const id2 = mySetTimeout(callback, 200);
    const id3 = mySetTimeout(callback, 300);
    
    expect(id1).toBeTypeOf('number');
    expect(id2).toBeTypeOf('number');
    expect(id3).toBeTypeOf('number');
    
    expect(id1).not.toBe(id2);
    expect(id2).not.toBe(id3);
    expect(id1).not.toBe(id3);
  });

  it('should handle zero delay', async () => {
    const callback = vi.fn();
    
    mySetTimeout(callback, 0);
    
    vi.advanceTimersByTime(0);
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should handle negative delay as zero', async () => {
    const callback = vi.fn();
    
    mySetTimeout(callback, -100);
    
    vi.advanceTimersByTime(0);
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should pass arguments to callback', async () => {
    const callback = vi.fn();
    
    mySetTimeout(callback, 100, 'hello', 42, { test: true });
    
    vi.advanceTimersByTime(100);
    
    expect(callback).toHaveBeenCalledWith('hello', 42, { test: true });
  });

  it('should cancel timer with clearTimeout', async () => {
    const callback = vi.fn();
    
    const timerId = mySetTimeout(callback, 1000);
    myClearTimeout(timerId);
    
    vi.advanceTimersByTime(1000);
    expect(callback).not.toHaveBeenCalled();
  });

  it('should handle clearing non-existent timer gracefully', () => {
    expect(() => {
      myClearTimeout(99999);
    }).not.toThrow();
  });

  it('should handle multiple concurrent timers', async () => {
    const callback1 = vi.fn();
    const callback2 = vi.fn();
    const callback3 = vi.fn();
    
    mySetTimeout(callback1, 100);
    mySetTimeout(callback2, 200);
    mySetTimeout(callback3, 300);
    
    vi.advanceTimersByTime(100);
    expect(callback1).toHaveBeenCalledTimes(1);
    expect(callback2).not.toHaveBeenCalled();
    expect(callback3).not.toHaveBeenCalled();
    
    vi.advanceTimersByTime(100);
    expect(callback1).toHaveBeenCalledTimes(1);
    expect(callback2).toHaveBeenCalledTimes(1);
    expect(callback3).not.toHaveBeenCalled();
    
    vi.advanceTimersByTime(100);
    expect(callback1).toHaveBeenCalledTimes(1);
    expect(callback2).toHaveBeenCalledTimes(1);
    expect(callback3).toHaveBeenCalledTimes(1);
  });

  it('should allow canceling some timers while others continue', async () => {
    const callback1 = vi.fn();
    const callback2 = vi.fn();
    const callback3 = vi.fn();
    
    const id1 = mySetTimeout(callback1, 100);
    const id2 = mySetTimeout(callback2, 200);
    const id3 = mySetTimeout(callback3, 300);
    
    myClearTimeout(id2); // Cancel middle timer
    
    vi.advanceTimersByTime(300);
    
    expect(callback1).toHaveBeenCalledTimes(1);
    expect(callback2).not.toHaveBeenCalled(); // Should be cancelled
    expect(callback3).toHaveBeenCalledTimes(1);
  });

  it('should handle clearing already executed timer', async () => {
    const callback = vi.fn();
    
    const timerId = mySetTimeout(callback, 100);
    
    vi.advanceTimersByTime(100);
    expect(callback).toHaveBeenCalledTimes(1);
    
    // Should not throw when clearing already executed timer
    expect(() => {
      myClearTimeout(timerId);
    }).not.toThrow();
  });

  it('should handle default delay parameter', async () => {
    const callback = vi.fn();
    
    mySetTimeout(callback); // No delay specified, should default to 0
    
    vi.advanceTimersByTime(0);
    expect(callback).toHaveBeenCalledTimes(1);
  });

  it('should reuse timer IDs after clearing', () => {
    const callback = vi.fn();
    
    const id1 = mySetTimeout(callback, 100);
    myClearTimeout(id1);
    
    const id2 = mySetTimeout(callback, 200);
    
    // ID should be reusable (implementation detail, but good practice)
    expect(typeof id2).toBe('number');
  });

  it('should handle rapid timer creation and cancellation', () => {
    const callback = vi.fn();
    const timerIds: number[] = [];
    
    // Create many timers
    for (let i = 0; i < 100; i++) {
      timerIds.push(mySetTimeout(callback, i * 10));
    }
    
    // Cancel every other timer
    for (let i = 0; i < timerIds.length; i += 2) {
      myClearTimeout(timerIds[i]);
    }
    
    vi.advanceTimersByTime(1000);
    
    // Should have executed 50 callbacks (every other one)
    expect(callback).toHaveBeenCalledTimes(50);
  });
});
