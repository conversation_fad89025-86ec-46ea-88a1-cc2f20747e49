# Interview Questions Repository

This repository contains TypeScript interview questions with test cases and implementation templates.

## 📁 Project Structure

```
interviews/
├── README.md                    # This file
├── package.json                 # Dependencies and scripts
├── vite.config.ts              # Vite configuration with Vitest
├── tsconfig.json               # TypeScript configuration
├── src/
│   └── vite-env.d.ts           # Vite type definitions
└── questions/                  # Interview questions folder
    └── array/                  # Array method implementations
        ├── map/
        │   ├── question.ts     # Problem description & function stub
        │   └── question.test.ts # Comprehensive test cases
        ├── filter/
        │   ├── question.ts
        │   └── question.test.ts
        ├── reduce/
        │   ├── question.ts
        │   └── question.test.ts
        └── forEach/
            ├── question.ts
            └── question.test.ts
```

## Structure

Each question is organized in its own folder with the following files:
- `question.ts` - The problem description and function stub to implement
- `question.test.ts` - Test cases to validate the implementation

## Available Questions

### Array Methods
- `array/map/` - Implement Array.prototype.map
- `array/filter/` - Implement Array.prototype.filter
- `array/reduce/` - Implement Array.prototype.reduce
- `array/forEach/` - Implement Array.prototype.forEach

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run tests:
   ```bash
   npm test
   ```

3. Run tests for a specific question:
   ```bash
   npm test questions/array/map
   ```

## How to Use

1. Pick a question folder (e.g., `questions/array/map/`)
2. Read the problem description in `question.ts`
3. Implement your solution by replacing the `throw new Error('Not implemented');` line
4. Run the tests to validate your implementation
5. All tests should pass when your implementation is correct

## Test Results

The tests are designed to fail initially since the implementations throw "Not implemented" errors. As you implement each function, the tests will start passing.

Example test run:
```bash
npm test questions/array/map
# Should show 7 failing tests initially
# Implement myMap function in question.ts
# Re-run tests to see them pass
```

## Features

- ✅ **TypeScript** with proper type definitions
- ✅ **Vitest** testing framework configured
- ✅ **Comprehensive test cases** covering edge cases
- ✅ **Function stubs** ready to be implemented
- ✅ **Detailed problem descriptions** with requirements
- ✅ **Type safety** with TypeScript interfaces

## Adding New Questions

To add a new question:

1. Create a new folder under `questions/`
2. Add `question.ts` with the problem description and function stub
3. Add `question.test.ts` with comprehensive test cases
4. Update this README with the new question

Happy coding! 🚀
