# Interview Questions Repository

This repository contains TypeScript interview questions with test cases and implementation templates.

## Structure

Each question is organized in its own folder under `questions/` with the following files:
- `question.ts` - The problem description and interface/type definitions
- `question-impl.ts` - Empty implementation class to be filled out
- `question.test.ts` - Test cases to validate the implementation

## Available Questions

### Array Methods
- `array-map/` - Implement Array.prototype.map
- `array-filter/` - Implement Array.prototype.filter  
- `array-reduce/` - Implement Array.prototype.reduce
- `array-forEach/` - Implement Array.prototype.forEach

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run tests:
   ```bash
   npm test
   ```

3. Run tests for a specific question:
   ```bash
   npm test questions/array-map
   ```

## How to Use

1. Read the problem description in `question.ts`
2. Implement the solution in `question-impl.ts`
3. Run the tests to validate your implementation
4. All tests should pass when the implementation is correct
